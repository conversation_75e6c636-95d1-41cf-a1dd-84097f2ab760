<script setup>
import useAddCoupon from "./utils/addCoupon.jsx";
import { PlusSearch, PlusTable } from "plus-pro-components";

// 使用组合式函数
const {
  // 路由相关
  router,
  route,

  // 表单相关
  form,
  formRef,
  formData,
  formData2,
  rules,

  // 使用范围相关
  scopeForm,
  searchColumns,
  tableColumns,
  tableData,
  stableTableData,
  showScopeSelection,

  // 分页相关
  pagination,

  // 状态相关
  loading,
  getListLoading,
  selectedRows,
  richFlag,
  formFile,

  // 方法
  onSearch,
  onReset,
  onPageChange,
  onSizeChange,
  onSelectionChange,
  onScopeChange,
  reset,
  submitForm,
  onSubmit,

  // 测试方法
  testScopeChange,
  // testApiData,
  // testCouponType,
  // testCourseQuery,
  testFormDefaults,
  testScopeSwitch,

  // 其他数据
  newData,
  oldData
} = useAddCoupon();
</script>

<template>
  <div class="main">
    <el-scrollbar class="scrollbar">
      <el-form
        ref="formRef"
        :scroll-to-error="true"
        :model="form"
        label-width="auto"
        :rules="rules"
      >
        <div>
          <div class="title-text">基本信息</div>
          <el-descriptions title="" :column="1" border class="my-descriptions">
            <el-descriptions-item
              v-for="(item, index) in formData"
              v-show="!item.show || item.show()"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <!-- input输入 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model.trim="form[item.prop]"
                    :placeholder="item.placeholder"
                    :style="{ width: item.width }"
                    :disabled="route.query.courseId ? true : false"
                    :maxlength="item.maxLength"
                    :show-word-limit="item.maxLength"
                  />
                </template>
                <!-- textarea输入 -->
                <template v-if="item.type === 'textarea'">
                  <el-input
                    v-model="form[item.prop]"
                    :rows="5"
                    type="textarea"
                    :style="{ width: item.width }"
                    :placeholder="item.placeholder"
                    :maxlength="item.maxLength"
                    :show-word-limit="item.maxLength"
                  />
                </template>
                <!-- 复选框 -->
                <template v-if="item.type === 'checkBox'">
                  <el-checkbox-group v-model="form[item.prop]">
                    <el-checkbox
                      v-for="it in item.options"
                      :key="it.value"
                      :value="item.value"
                      name="type"
                    >
                      {{ it.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </template>
                <!-- 单选框 -->
                <template v-if="item.type === 'radio'">
                  <el-radio-group v-model="form[item.prop]">
                    <el-radio
                      v-for="it in item.options"
                      :key="it.value"
                      :value="it.value"
                    >
                      {{ it.name }}
                    </el-radio>
                  </el-radio-group>
                </template>
                <!-- 日期选择 -->
                <template v-if="item.type === 'date'">
                  <el-date-picker
                    v-model="form[item.prop]"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="YYYY-MM-DD"
                  />
                </template>
                <!-- 单选框+输入框 -->
                <template v-if="item.type === 'radioInput'">
                  <div>
                    <span style="margin-right: 10px">满</span>
                    <el-input
                      v-model="form.maxAmount"
                      placeholder="请输入金额"
                      style="width: 200px; margin-right: 20px"
                    />
                    <span style="margin-right: 10px">减</span>
                    <el-input
                      v-model="form.minAmount"
                      placeholder="请输入金额"
                      style="width: 200px"
                    />
                  </div>
                </template>
                <template v-if="item.type === 'dateRadio'">
                  <el-radio-group v-model="form[item.prop]">
                    <el-radio
                      v-for="it in item.options"
                      :key="it.value"
                      :value="it.value"
                    >
                      {{ it.name }}
                    </el-radio>
                  </el-radio-group>
                  <div>
                    <el-date-picker
                      v-model="form[item.prop]"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      value-format="YYYY-MM-DD"
                    />
                  </div>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <div class="title-text">优惠券使用范围</div>
          <el-descriptions title="" :column="1" border class="my-descriptions">
            <el-descriptions-item
              v-for="(item, index) in formData2"
              :key="index"
              label-align="right"
              label-class-name="my-label"
              :span="item.span || 1"
            >
              <template #label>
                <span v-if="item.check" class="star">*</span>{{ item.label }}
              </template>
              <el-form-item
                :prop="item.prop"
                :inline-message="item.check"
                style="margin-bottom: 0"
                :show-message="true"
                error-placement="right"
              >
                <template v-if="item.type === 'radio'">
                  <el-radio-group
                    v-model="form[item.prop]"
                    @change="onScopeChange"
                  >
                    <el-radio
                      v-for="it in item.options"
                      :key="it.value"
                      :value="it.value"
                    >
                      {{ it.name }}
                    </el-radio>
                  </el-radio-group>
                </template>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 使用范围选择区域 - 当选择"指定"时显示 -->
          <div v-if="showScopeSelection" class="scope-selection-area">
            <!-- 搜索区域 -->
            <div class="search-container">
              <PlusSearch
                v-model="scopeForm"
                :columns="searchColumns"
                :show-number="3"
                :label-width="120"
                :hasUnfold="false"
                :searchOnChange="false"
                :gutter="20"
                @search="onSearch"
                @reset="onReset"
              />
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
              <PlusTable
                v-loading="loading"
                row-key="id"
                :data="tableData"
                :columns="tableColumns"
                :title-bar="false"
                :border="false"
                :is-selection="true"
                @selection-change="onSelectionChange"
              />
            </div>

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page + 1"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="onSizeChange"
                @current-change="onPageChange"
              />
            </div>

            <!-- 选择信息提示 -->
            <div v-if="showScopeSelection" class="selection-info">
              已选择
              <span class="selected-count">{{ selectedRows.length }}</span>
              个课程
            </div>
            <div v-else class="selection-info">
              <span style="color: #909399">通用模式：所有课程均可使用</span>
            </div>
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <div class="footer">
      <el-button @click="reset">取消</el-button>
      <el-button type="primary" :loading="getListLoading" @click="submitForm">
        确认
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main {
  // padding: 20px;
  background: #fff;
}
.scrollbar {
  // padding-top: 20px;
  padding: 20px;
  // height: calc(100vh - 201px);
  height: calc(100vh - 171px);
  background-color: #fff;
}
.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
  padding: 0 10px;

  .title_left {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }

  .title_rigth {
    display: flex;

    :nth-child(2) {
      margin-left: 40px;
    }
  }
}

.account_management {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;
  font-weight: 500;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;

  .footer_left {
    padding: 8px 18px;
    cursor: pointer;
    background: red;
    border-radius: 8px;
  }

  .footer_right {
    padding: 8px 18px;
    cursor: pointer;
    background: #4095e5;
    border-radius: 8px;
  }
}

:deep(.my-label) {
  background: #fff !important;
  min-width: 150px !important;
  // width: 250px;
}

:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}

.star {
  margin-right: 3px;
  color: red;
}

.input_box {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.upload-demo {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.fileOther {
  // margin-top: 26px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  line-height: 22px;
  width: 420px;
  // margin-bottom: 56px;
  .title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    white-space: nowrap;
  }
  .link {
    width: 20px;
    height: 20px;
    margin: 0 10px;
    cursor: pointer;
  }
  .fileName {
    color: #3876fd;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 400;
    height: 24px;
    cursor: pointer;
    // width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkDetele {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
:deep(.el-form-item__content) {
  flex-direction: column;
  align-items: normal;
}
:deep(.el-form-item__content) {
  display: inline-block;
  width: 300px;
  .upload_text {
    display: inline-block;
    font-size: 12px;
    position: relative;
    top: 5px;
    color: #8c939d;
  }
}
.title-text {
  color: #0e1832;
  font-family: Source Han Sans CN;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 20px 0 20px 10px;

  &::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #e4e7ed;
    margin-left: 16px;
  }
}
.upload_text {
  display: inline-block;
  font-size: 12px;
  position: relative;
  top: 5px;
  color: #8c939d;
}
:deep(
  .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell
) {
  border: 0px !important;
}
.selsect-pos {
  position: relative;
  width: 100%;
  // height: 60px;
  // background: red;
  .cover {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    // background: red;
    width: 100%;
    height: 100%;
  }

  .pos-icon {
    width: 24px;
    height: 24px;
  }
}
:deep(.hideUploadBtn .el-upload--picture-card) {
  display: none;
}
:deep(.el-form-item__error) {
  position: absolute;
  left: 0;
  top: 100%;
  white-space: nowrap;
  z-index: 10;
}
:deep(.el-form-item__content) {
  position: relative;
  min-height: 40px; // 仍建议设置
}
.demo-date-picker {
  display: flex;
  width: 100%;
  padding: 0;
  flex-wrap: wrap;
}

.demo-date-picker .block {
  padding: 30px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  flex: 1;
}

.demo-date-picker .block:last-child {
  border-right: none;
}

.demo-date-picker .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}

/* 使用范围选择区域样式 */
.scope-selection-area {
  margin-top: 20px;
  // background-color: #0e1832;
}

.search-container {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  // border: 1px solid #e4e7ed;
  // height: 300px;

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
    text-align: right;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }

  /* 确保标签有足够空间 */
  :deep(.el-form-item__label) {
    width: 80px !important;
    text-align: right;
    padding-right: 12px;
    white-space: nowrap;
    overflow: visible;
  }

  /* 优化表单项布局 */
  :deep(.el-form-item) {
    margin-bottom: 16px;
    margin-right: 20px;
    width: 200px;
  }

  /* 确保输入框有足够空间 */
  :deep(.el-form-item__content) {
    flex: 1;
    min-width: 270px;
  }
}

.table-container {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  // border: 1px solid #e4e7ed;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  // border: 1px solid #e4e7ed;
}

.selection-info {
  text-align: right;
  padding: 12px 20px;
  color: #606266;
  font-size: 14px;
  background-color: #fff;
  border-radius: 4px;
  // border: 1px solid #e4e7ed;
}

.selected-count {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

/* 表格样式 */
:deep(.no-border-table .el-table) {
  border: none;
}

:deep(.no-border-table .el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.no-border-table .el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.no-border-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.search-container .el-form-item) {
  margin-bottom: 16px;
}

:deep(.search-container .el-col) {
  max-width: 400px !important;
  min-width: 300px !important;
}

/* 确保搜索区域有足够空间 */
:deep(.search-container .el-row) {
  margin: 0 !important;
}

:deep(.search-container .el-col) {
  padding: 0 10px !important;
}

/* 搜索按钮样式 */
:deep(.search-container .el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.search-container .el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(
  .pagination
    .el-pagination.is-background
    .el-pager
    li:not(.is-disabled).is-active
) {
  background-color: #409eff;
}

/* 选择框样式 */
:deep(.no-border-table .el-table__selection) {
  .el-checkbox__inner {
    border-color: #dcdfe6;
  }
}

:deep(
  .no-border-table
    .el-table__selection
    .el-checkbox__input.is-checked
    .el-checkbox__inner
) {
  background-color: #409eff;
  border-color: #409eff;
}
</style>
