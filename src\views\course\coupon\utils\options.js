import dayjs from "dayjs";
import { COUPON_FREE_TYPE } from "@/utils/enum";
//有效优惠券列表
export const columns = [
  {
    label: "优惠券名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "发放时间（开始）",
    prop: "distributionStartTime",
    minWidth: 180,
    formatter: ({ distributionStartTime }) => {
      return distributionStartTime
        ? dayjs(distributionStartTime).format("YYYY-MM-DD")
        : "--";
    }
  },
  {
    label: "使用时间",
    prop: "startTime",
    minWidth: 230,
    formatter: ({ startTime, endTime }) => {
      if (startTime && endTime) {
        return `${dayjs(startTime).format("YYYY-MM-DD")} - ${dayjs(endTime).format("YYYY-MM-DD")}`;
      } else if (startTime) {
        return `${dayjs(startTime).format("YYYY-MM-DD")}`;
      } else if (endTime) {
        return `${dayjs(endTime).format("YYYY-MM-DD")}`;
      } else {
        return "--";
      }
    }
  },
  {
    label: "领取数量/发放数量",
    prop: "receiveCount",
    minWidth: 90,
    formatter: ({ receivedNumber, totalIssue }) => {
      const received = receivedNumber || 0;
      const total = totalIssue || 0;
      const percentage =
        total > 0
          ? (() => {
              const value = ((received / total) * 100).toFixed(2);
              return parseFloat(value) % 1 === 0 ? parseInt(value) : value;
            })()
          : 0;
      return `${received}/${total} (${percentage}%)`;
    }
  },
  {
    label: "优惠规则",
    prop: "discountRule",
    minWidth: 90,
    formatter: ({ couponDiscountType, discountAmount, conditionAmount }) => {
      if (couponDiscountType === "FULL_REDUCTION") {
        return `满${conditionAmount || 0}减${discountAmount || 0}`;
      } else if (couponDiscountType === "DISCOUNT") {
        return `${discountAmount || 0}折`;
      } else if (couponDiscountType === "FIXED") {
        return `立减${discountAmount || 0}`;
      }
      return "--";
    }
  },
  {
    label: "使用范围",
    minWidth: 180,
    prop: "couponScope",
    formatter: ({ couponScope }) => {
      if (couponScope === "ALL") {
        return "通用";
      } else {
        return "指定";
      }
    }
  },
  {
    label: "类型",
    minWidth: 180,
    prop: "feeType",
    formatter: ({ feeType }) => {
      return feeType ? COUPON_FREE_TYPE[feeType].label : "--";
    }
  },
  {
    label: "状态",
    minWidth: 200,
    slot: "enabled"
  },
  {
    label: "操作",
    fixed: "right",
    width: 320,
    slot: "operation"
  }
];
//已失效效优惠券列表
export const failureColumns = [
  {
    label: "优惠券名",
    prop: "name",
    minWidth: 90,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "发放时间（开始）",
    prop: "distributionStartTime",
    minWidth: 180,
    formatter: ({ distributionStartTime }) => {
      return distributionStartTime
        ? dayjs(distributionStartTime).format("YYYY-MM-DD")
        : "--";
    }
  },
  {
    label: "使用时间",
    prop: "startTime",
    formatter: ({ startTime, endTime }) => {
      if (startTime && endTime) {
        return `${dayjs(startTime).format("YYYY-MM-DD")} 至 ${dayjs(endTime).format("YYYY-MM-DD")}`;
      } else if (startTime) {
        return `${dayjs(startTime).format("YYYY-MM-DD")}`;
      } else if (endTime) {
        return `${dayjs(endTime).format("YYYY-MM-DD")}`;
      } else {
        return "--";
      }
    }
  },
  {
    label: "领取数量/发放数量",
    prop: "receiveCount",
    minWidth: 90,
    formatter: ({ receivedNumber, totalIssue }) => {
      const received = receivedNumber || 0;
      const total = totalIssue || 0;
      const percentage =
        total > 0
          ? (() => {
              const value = ((received / total) * 100).toFixed(2);
              return parseFloat(value) % 1 === 0 ? parseInt(value) : value;
            })()
          : 0;
      return `${received}/${total} (${percentage}%)`;
    }
  },
  {
    label: "优惠规则",
    prop: "discountRule",
    minWidth: 90,
    formatter: ({ couponDiscountType, discountAmount, conditionAmount }) => {
      if (couponDiscountType === "FULL_REDUCTION") {
        return `满${conditionAmount || 0}减${discountAmount || 0}`;
      } else if (couponDiscountType === "DISCOUNT") {
        return `${discountAmount || 0}折`;
      } else if (couponDiscountType === "FIXED") {
        return `立减${discountAmount || 0}`;
      }
      return "--";
    }
  },
  {
    label: "使用范围",
    minWidth: 180,
    prop: "couponScope",
    formatter: ({ couponScope }) => {
      if (couponScope === "ALL") {
        return "通用";
      } else {
        return "指定";
      }
    }
  },
  {
    label: "类型",
    minWidth: 180,
    prop: "feeType",
    formatter: ({ feeType }) => {
      return feeType ? COUPON_FREE_TYPE[feeType].label : "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 150,
    slot: "operation"
  }
];
//优惠费用类型
export const discountType = [
  { name: "不限", id: 1 },
  { name: "课时", id: "CLASS_HOUR" },
  { name: "材料", id: "MATERIAL" }
];
//状态
export const discountStatus = [
  { name: "全部", id: 1 },
  { name: "启用", id: true },
  { name: "停用", id: false }
];
//使用范围
export const scopeOptions = [
  { name: "不限", id: 1 },
  { name: "通用", id: "ALL" },
  { name: "指定", id: "LIMIT" }
];
