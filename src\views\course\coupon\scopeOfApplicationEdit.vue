<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch, PlusTable } from "plus-pro-components";
import { ElMessage, ElMessageBox } from "element-plus";
import CouponDetailInfo from "./components/couponDetailInfo.vue";
import { getCouponCourseList, createOrUpdateCoupon } from "@/api/coupon.js";

defineOptions({
  name: "ScopeOfApplicationEdit"
});

const router = useRouter();
const route = useRoute();

// 搜索表单数据
const searchForm = reactive({
  courseName: "",
  coursePeriodName: "",
  coursePeriodState: ""
});

// 搜索列配置
const searchColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    valueType: "input",
    placeholder: "请输入课程名称"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    valueType: "input",
    placeholder: "请输入课期名称"
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    valueType: "select",
    fieldProps: {
      placeholder: "全部",
      clearable: true,
      style: { width: "200px" }
    },
    options: [
      {
        label: "全部",
        value: ""
      },
      {
        value: "ONLINE",
        label: "上架"
      },
      {
        value: "NOT_LISTED",
        label: "未上架"
      },
      {
        value: "ONLINE_UNDER_REVIEW",
        label: "上架审核"
      },
      {
        value: "OFFLINE",
        label: "下架"
      },
      {
        value: "OFFLINE_UNDER_REVIEW",
        label: "下架审核"
      },
      {
        value: "COMPLETED",
        label: "已完成"
      }
    ]
  }
]);

// 表格数据
const tableData = ref([]);

// 分页配置
const pagination = reactive({
  page: 0, // API从0开始
  size: 20,
  total: 0
});

// 加载状态
const loading = ref(false);

// 选中的行
const selectedRows = ref([]);

// 存储选中行的详细信息
const rowList = ref({
  coursePeriodId: null,
  couponAvailable: null,
  couponIds: []
});

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    };

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (
        params[key] === "" ||
        params[key] === null ||
        params[key] === undefined
      ) {
        delete params[key];
      }
    });

    const response = await getCouponCourseList(params);

    if (response.code === 200) {
      tableData.value = response.data.content || [];
      pagination.total = response.data.totalElements || 0;
      pagination.page = response.data.number || 0;
      pagination.size = response.data.size || 20;
    } else {
      ElMessage.error(response.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 搜索方法
const onSearch = () => {
  pagination.page = 0; // 搜索时重置到第一页
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  };
  fetchTableData();
};

// 重置方法
const onReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  pagination.page = 0;
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  };
  fetchTableData();
};

// 分页改变
const onPageChange = page => {
  pagination.page = page - 1; // 转换为API的从0开始
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  };
  fetchTableData();
};

// 每页条数改变
const onSizeChange = size => {
  pagination.size = size;
  pagination.page = 0;
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  };
  fetchTableData();
};

// 选择改变
const onSelectionChange = selection => {
  selectedRows.value = selection || [];

  // 更新rowList，存储选中行的详细信息
  if (selection && selection.length > 0) {
    // 如果只选择了一行，直接设置
    // if (selection.length === 1) {
    //   const row = selection[0];
    //   rowList.value = {
    //     coursePeriodId: row.id,
    //     couponAvailable: true,
    //     couponIds: [row.id] // 使用课期ID作为优惠券ID
    //   };
    // } else {
    // 如果选择了多行，使用第一行作为主要信息，但记录所有选中的ID
    // console.log(route.query.coursePeriodId);
    // const firstRow = selection[0];
    rowList.value = {
      coursePeriodId: Number(route.query?.coursePeriodId),
      couponAvailable: true,
      couponIds: selection.map(row => row.id) // 所有选中行的ID
    };
    // }
  } else {
    // 没有选择时，清空rowList
    rowList.value = {
      coursePeriodId: null,
      couponAvailable: null,
      couponIds: []
    };
  }
};

// 确认修改
const onConfirm = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请至少选择一个课期");
    return;
  }

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要为选中的 ${selectedRows.value.length} 个课期设置优惠券使用范围吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    loading.value = true;

    // 根据选择的行数决定调用方式
    // if (selectedRows.value.length === 1) {
    //   // 单行选择，直接调用接口
    //   const params = {
    //     coursePeriodId: rowList.value.coursePeriodId,
    //     couponAvailable: rowList.value.couponAvailable,
    //     couponIds: rowList.value.couponIds
    //   };

    //   const response = await createOrUpdateCoupon(params);

    //   if (response.code === 200 || response.code === 0) {
    //     ElMessage.success("修改成功");
    //     // 重新加载数据
    //     fetchTableData();
    //     // 清空选择
    //     selectedRows.value = [];
    //     rowList.value = {
    //       coursePeriodId: null,
    //       couponAvailable: null,
    //       couponIds: []
    //     };
    //   } else {
    //     ElMessage.error(response.msg || "修改失败");
    //   }
    // } else {
    // 多行选择，批量调用接口
    // const promises = selectedRows.value.map(row => {
    //   const params = {
    //     coursePeriodId: row.id,
    //     couponAvailable: true,
    //     couponIds: [row.id] // 每个课期使用自己的ID
    //   };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `修改了${selectedRows.value.length}条使用范围信息`
    };

    const { code, data } = await createOrUpdateCoupon(
      rowList.value,
      operateLog
    );
    console.log("🌈-----results-----", data);
    // });
    // const resultList = resultList.push(results);

    // const results = await Promise.all(promises);

    // 检查所有请求是否成功
    // const successCount = resultList.filter(
    //   result => result.code === 200 || result.code === 0
    // ).length;
    // const failCount = resultList.length - successCount;

    if (code === 200) {
      ElMessage.success(`成功修改优惠券使用范围`);
      // 重新加载数据
      fetchTableData();
      // 清空选择
      selectedRows.value = [];
      rowList.value = {
        coursePeriodId: null,
        couponAvailable: null,
        couponIds: []
      };
    }
    // else {
    //   ElMessage.warning(`成功: ${successCount} 个，失败: ${failCount} 个`);
    // }
    // }
  } catch (error) {
    if (error !== "cancel") {
      console.error("修改失败:", error);
      ElMessage.error("修改失败，请稍后重试");
    }
  } finally {
    loading.value = false;
  }
};

// 取消操作
const onCancel = () => {
  // ElMessage.info("已取消修改");
  router.go(-1);
};

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 150px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 340px - ${searchFormHeight.value}px)`;
  }
};

// 表格列配置
const tableColumns = ref([
  {
    label: "",
    type: "selection",
    width: 55
    // align: "center"
  },
  {
    label: "期号",
    prop: "termNumber",
    width: 100,
    align: "center"
  },
  {
    label: "课期名称",
    prop: "name"
    // align: "center"
    // minWidth: 200
  },
  {
    label: "开课时间",
    prop: "openTime",
    width: 220,
    // align: "center",
    render: (cellValue, row) => {
      if (row.row.openTime) {
        return new Date(row.row.openTime).toLocaleDateString("zh-CN");
      }
      return "-";
    }
  },
  {
    label: "购买类型",
    prop: "buyType",
    width: 100,
    // align: "center",
    render: (cellValue, row) => {
      const buyTypeMap = {
        ORDINARY: "普通单",
        PRIVATE_DOMAIN_GROUP_ORDER: "私域团购单"
      };
      return buyTypeMap[row.row.buyType] || row.row.buyType || "-";
    }
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    width: 100,
    // align: "center",
    render: (cellValue, row) => {
      const statusMap = {
        ONLINE: "上架",
        NOT_LISTED: "未上架",
        ONLINE_UNDER_REVIEW: "上架审核中",
        OFFLINE: "下架",
        OFFLINE_UNDER_REVIEW: "下架审核中",
        COMPLETED: "已完成"
      };
      const status =
        statusMap[row.row.coursePeriodState] || row.row.coursePeriodState;
      return status;
    }
  }
]);

onMounted(() => {
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);

  // 初始加载数据
  fetchTableData();
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- 优惠券详情 -->
      <div class="coupon-detail">
        <CouponDetailInfo :showUseScope="false" />
      </div>

      <div class="con-content">
        <!-- 搜索区域 -->
        <div class="con_search">
          <PlusSearch
            v-model="searchForm"
            :columns="searchColumns"
            :show-number="3"
            :label-width="80"
            :hasUnfold="false"
            :searchOnChange="false"
            @search="onSearch"
            @reset="onReset"
          />
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <PlusTable
            v-loading="loading"
            :data="tableData"
            :columns="tableColumns"
            :title-bar="false"
            :border="false"
            :is-selection="true"
            :max-height="tableHeight"
            @selection-change="onSelectionChange"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.page + 1"
            :page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            width="100%"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="onCancel">取消</el-button>
          <el-button
            type="primary"
            :disabled="selectedRows.length === 0"
            @click="onConfirm"
          >
            确认
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.breadcrumb {
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.coupon-detail {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
}

.label {
  color: #606266;
  font-weight: 500;
  min-width: 120px;
  margin-right: 8px;
}

.value {
  color: #303133;
  flex: 1;
}

.status-enabled {
  color: #67c23a;
  font-weight: 500;
}

.con_search {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  /* margin-bottom: 16px; */

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

.table-container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  height: 320px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin: 60px 0 16px 0;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 0 20px 15px 0;
  background-color: #fff;
}

/* 表格样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-col) {
  max-width: 330px !important;
}

/* 搜索按钮样式 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

/* 选择框样式 */
:deep(.el-table__selection) {
  .el-checkbox__inner {
    border-color: #dcdfe6;
  }
}

:deep(.el-table__selection .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

/* 响应式设计 */
/* @media (max-width: 768px) {
  .containers {
    padding: 10px;
  }

  .detail-row {
    flex-direction: column;
    gap: 12px;
  }

  .detail-item {
    min-height: auto;
  }
} */
.con-content {
  height: 590px;
  background-color: #ffffff;
}
</style>
