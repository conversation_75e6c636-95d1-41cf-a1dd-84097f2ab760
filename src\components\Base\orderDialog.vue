<script setup>
import { id } from "element-plus/es/locale/index.mjs";
import { onMounted, ref, defineEmits, computed, reactive, nextTick } from "vue";
import { requestTo } from "@/utils/http/tool";
import { pinyin } from "pinyin-pro";
import { decrypt, encryption } from "@/utils/SM4.js";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import baseUrl, { codeInfo } from "@/utils/http/base.js";
import dayjs from "dayjs";
import { COUPON_FREE_TYPE } from "@/utils/enum.js";
import { debounce } from "@iceywu/utils";
const props = defineProps({
  title: {
    type: String
  },
  api: {
    type: String,
    default: ""
  },
  id: {
    type: Number
  },
  name: {
    type: String,
    default: ""
  },
  phone: {
    type: String,
    default: ""
  },
  account: {
    type: String,
    default: ""
  },
  dialogFormVisible: {
    type: Boolean
  },
  logOut: {
    type: Boolean,
    default: true
  },
  operateLogType: {
    type: String,
    default: "COMPLEX_MANAGEMENT"
  },
  textLeftBtn: {
    type: String,
    default: "取消"
  },
  textRightBtn: {
    type: String,
    default: "确认"
  },
  operateType: {
    type: String,
    default: "重置了密码"
  },
  showContent: {
    type: String,
    default: "resetPassword"
  },
  marginLeft: {
    type: String,
    default: ""
  },
  dataInfo: {
    type: Object,
    default: () => {}
  },
  couponTarget: {
    type: String,
    default: ""
  },
  width: {
    type: Number,
    default: 515
  }
});
const emit = defineEmits(["reset", "update:dialogFormVisible", "updateData"]);
const router = useRouter();
const dispatchFormRef = ref();
// 表单验证规则
const dispatchRules = reactive({
  phone: [{ required: true, message: "请输入用户账号", trigger: "blur" }]
});
const dispatchForm = ref({
  phone: ""
});
onMounted(() => {
  generatePassword();
});
// const dialogFormVisible = ref(false);
const newPassword = ref("");
const getListLoading = ref(false);
const btnOKClick = debounce(
  async data => {
    if (getListLoading.value) {
      return;
    }

    getListLoading.value = true;
    let paramsData = {
      id: props.id,
      password: encryption(newPassword.value)
    };
    let operateLog = {
      operateLogType: props.operateLogType,
      operateType: props.operateType || "重置了密码"
      // operatorTarget: form.value.name,
    };
    if (props.showContent === "coupon") {
      if (props.couponTarget === "distributeCoupon") {
        dispatchFormRef.value.validate(valid => {
          if (valid) {
            emit("reset", dispatchForm.value.phone);
          }
        });
        getListLoading.value = false;
        return;
      } else if (props.couponTarget === "confirmCoupon") {
        paramsData = {
          id: props.dataInfo?.id,
          phone: encryption(props.dataInfo?.phone)
        };
      }
    }
    if (props.showContent === "groupOrder") {
      paramsData = {
        coursePeriodId: props.id,
        qrCode: {
          scene: props.id,
          // page: "zhongshan-special/home/<USER>",
          ...codeInfo.qrParams
        }
      };
    }
    if (props.showContent === "couponStopStart") {
      if (props.couponTarget === "stopCoupon") {
        paramsData = {
          id: props.dataInfo?.id || 0,
          invalidate: false
        };
      } else if (props.couponTarget === "startCoupon") {
        paramsData = {
          id: props.dataInfo?.id || 0,
          invalidate: true
        };
      }
    }
    try {
      const { code, data, msg } = await props.api(paramsData, operateLog);
      if (code === 200) {
        ElMessage({
          message: "重置密码成功",
          type: "success"
        });
        if (props.showContent === "groupOrder") {
          router.push({
            path: "/course/currentDetails/groupOrder",
            query: { periodId: props.id, ordersId: data.ordersId || "" }
          });
        } else if (props.showContent === "coupon") {
          ElMessage({
            message: `${props.dataInfo.phone}用户派发成功`,
            type: "success"
          });
          dispatchForm.value.phone = "";
        } else if (props.showContent === "couponStopStart") {
          ElMessage.closeAll();
          await nextTick();
          ElMessage({
            message:
              props.couponTarget === "stopCoupon"
                ? `停用“${props.dataInfo.name}”优惠券成功`
                : `启用“${props.dataInfo.name}”优惠券成功`,
            type: "success",
            zIndex: 9999,
            customClass: "coupon-message",
            grouping: true
          });
        } else {
          ElMessage({
            message: "重置密码成功",
            type: "success"
          });
          /** 退出登录 */
          if (props.logOut) {
            useUserStoreHook().logOut();
          }
        }
        emit("updateData");
      } else {
        if (props.showContent === "groupOrder") {
          if (code === 30039) {
            ElMessage.error(`课程定制失败，${msg}，请去编辑`);
          } else if (code === 30040) {
            ElMessage.error(`课程定制失败，${msg}，请去检查`);
          } else if (code === 30034) {
            ElMessage.error(`课程定制失败，开课时间已过期`);
          } else {
            ElMessage.error(`课程定制失败，${msg}`);
          }
        }
        if (props.showContent === "coupon") {
          ElMessage.error(`${msg}，派发失败`);
        }
        if (props.showContent === "couponStopStart") {
          ElMessage({
            message:
              props.couponTarget === "stopCoupon"
                ? `停用${props.dataInfo?.name}优惠券失败，${msg}`
                : `启用${props.dataInfo?.name}优惠券失败，${msg}`,
            type: "error"
          });
        }
      }
    } catch (error) {
      if (props.showContent === "groupOrder") {
        ElMessage.error(error.message || error);
      } else if (props.showContent === "couponStopStart") {
        ElMessage.error(
          props.couponTarget === "stopCoupon"
            ? `停用优惠券失败: ${error.message || error}`
            : `启用优惠券失败: ${error.message || error}`
        );
      } else {
        console.error("操作失败：", error);
        ElMessage.error("操作失败，请稍后重试");
      }
    }
    getListLoading.value = false;
    emit("reset");
  },
  1000,
  { immediate: true }
);
// 生成管理员账号的方法
const generatePassword = () => {
  try {
    // 1. 获取姓名首字母
    const initials = pinyin(props.name, {
      pattern: "first", // 只保留首字母
      toneType: "none", // 不显示声调
      type: "array" // 返回数组格式
    })
      .join("")
      .toLowerCase();

    // 2. 获取手机号后6位
    const phonePart = props.phone.slice(-6);

    // 3. 组合生成密码
    newPassword.value = `${initials}${phonePart}@`;
    console.log("🌈-----newPassword.value-----", newPassword.value);
  } catch (error) {
    console.error("生成密码失败：", error);
    // password.value = '生成失败，请检查输入';
  }
};
const cancel = () => {
  dispatchForm.value.phone = "";
  emit("reset");
};
const handleClose = () => {
  dispatchForm.value.phone = "";
};
const timeFn = (val, type) => {
  if (type === "distributionTime") {
    if (val?.distributionStartTime && val?.distributionEndTime) {
      return `${dayjs(val?.distributionStartTime).format("YYYY-MM-DD")} 至 ${dayjs(val?.distributionEndTime).format("YYYY-MM-DD")}`;
    } else if (val?.distributionStartTime) {
      return `${dayjs(val?.distributionStartTime).format("YYYY-MM-DD")}`;
    } else if (val?.distributionEndTime) {
      return `${dayjs(val?.distributionEndTime).format("YYYY-MM-DD")}`;
    } else {
      return "--";
    }
  } else {
    if (val?.startTime && val?.endTime) {
      return `${dayjs(val?.startTime).format("YYYY-MM-DD")} 至 ${dayjs(val?.endTime).format("YYYY-MM-DD")}`;
    } else if (val?.startTime) {
      return `${dayjs(val?.startTime).format("YYYY-MM-DD")}`;
    } else if (val?.endTime) {
      return `${dayjs(val?.endTime).format("YYYY-MM-DD")}`;
    } else {
      return "--";
    }
  }
};
// 优惠规则
const discountRuleFn = val => {
  if (val?.couponDiscountType === "FULL_REDUCTION") {
    return `满${val?.conditionAmount || 0}减${val?.discountAmount || 0}`;
  } else if (val?.couponDiscountType === "DISCOUNT") {
    return `${val?.discountAmount || 0}折`;
  } else if (val?.couponDiscountType === "FIXED") {
    return `立减${val?.discountAmount || 0}`;
  } else {
    return "--";
  }
};
// 使用 computed 属性代理 prop
const localVisible = computed({
  get() {
    return props.dialogFormVisible;
  },
  set(value) {
    emit("update:dialogFormVisible", value); // 通知父组件更新
  }
});
</script>

<template>
  <!-- <div class="popup"> -->
  <el-dialog
    v-model="localVisible"
    :title="title || '重置密码确定'"
    :width="props.width || 515"
    @close="handleClose"
  >
    <div class="content">
      <div v-if="showContent === 'resetPassword'" class="info">
        <p>重置密码的账号 {{ props.account }}</p>
        <p>密码将被重置为 {{ newPassword }}</p>
        <p>密码生成规则：姓名首字母+手机号后6位+@</p>
      </div>
      <div v-if="showContent === 'groupOrder'" class="describe">
        <p>说明:</p>
        <p>1、一旦开启课程定制，该课程将无法在平台上进行公开展示，</p>
        <p>只能通过分享链接的方式让特定人群进行购买。</p>
        <p>
          2、一旦开启课程定制，该课程的所有信息将无法被修改。如需修改请取消定制后再进行修改。
        </p>
        <p>3、如果一旦有用户付款下单且没有退款，则无法取消定制。</p>
      </div>
      <div v-if="showContent === 'coupon'" class="coupon-describe">
        <div class="dispatch-info">
          <div class="info-item">
            <span class="label">优惠券名称：</span>
            <span class="value">{{ dataInfo?.name || "--" }}</span>
          </div>
          <div class="info-item">
            <span class="label">优惠券费用类型：</span>
            <span class="value">{{
              COUPON_FREE_TYPE[dataInfo?.feeType] || "--"
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">优惠门槛与金额：</span>
            <span class="value">{{ discountRuleFn(dataInfo) || "--" }}</span>
          </div>
          <div class="info-item">
            <span class="label">使用时间：</span>
            <span class="value">{{ timeFn(dataInfo, "useTime") || "--" }}</span>
          </div>
          <div v-if="couponTarget === 'confirmCoupon'" class="info-item">
            <span class="label">领券用户：</span>
            <span class="value">{{ dataInfo?.phone || "--" }}</span>
          </div>
        </div>

        <el-form
          v-if="couponTarget === 'distributeCoupon'"
          ref="dispatchFormRef"
          :model="dispatchForm"
          :rules="dispatchRules"
          label-width="0"
          style="margin-top: 20px"
        >
          <el-form-item prop="phone">
            <el-input
              v-model="dispatchForm.phone"
              placeholder="请输入用户账号"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="showContent === 'couponStopStart'" class="stop-describe">
        <div class="dispatch-info">
          <div class="info-item">
            <div v-if="couponTarget === 'stopCoupon'">
              确认是否要停用该优惠券？
            </div>
            <div v-if="couponTarget === 'startCoupon'">
              确认是否要启用该优惠券？
            </div>
          </div>
          <div class="info-item">
            <span class="label">优惠券名称：</span>
            <span class="value">{{ dataInfo?.name || "--" }}</span>
          </div>
          <div class="info-item">
            <span class="label">发放时间：</span>
            <span class="value">{{
              timeFn(dataInfo, "distributionTime") || "--"
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button :style="{ 'margin-right': marginLeft }" @click="cancel">
          {{ textLeftBtn }}
        </el-button>
        <el-button
          :loading="getListLoading"
          :type="
            textRightBtn === '确认'
              ? 'primary'
              : textRightBtn === '确认开启'
                ? 'primary'
                : 'danger'
          "
          @click="btnOKClick"
        >
          {{ textRightBtn }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  :nth-child(2) {
    margin: 20px 0 20px 0px;
  }
}
.describe {
  width: 80%;
  height: 130px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.coupon-describe {
  width: 80%;
  height: 180px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.stop-describe {
  width: 80%;
  height: 100px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
.dispatch-info {
  .info-item {
    display: flex;
    margin-bottom: 12px;
    font-size: 14px;

    .label {
      color: #606266;
      width: 120px;
      flex-shrink: 0;
      text-align: right; /* 左对齐 */
    }

    .value {
      color: #303133;
      font-weight: 500;
      flex: 1; /* 占据剩余空间 */
    }
  }
}
.coupon-message {
  z-index: 9999 !important;
  position: fixed !important;
  top: 100px !important;
}
</style>
