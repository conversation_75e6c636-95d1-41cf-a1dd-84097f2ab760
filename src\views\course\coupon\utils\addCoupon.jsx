import { ref, reactive, computed, nextTick, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { debounce, removeEmptyValues } from "@iceywu/utils";
import { addCoupon, getCouponCourse, getCouponFindById } from "@/api/coupon.js";
import { formatTime } from "@/utils/index";
// import { removeEmptyValues } from "@iceywu/utils";
// import { ElMessage } from "element-plus";

export default function useAddCoupon() {
  const router = useRouter();
  const route = useRoute();

  // 表单数据 - 根据API文档字段映射
  const form = reactive({
    name: "", // 优惠券名称
    feeType: "CLASS_HOUR", // 费用类型: CLASS_HOUR,INSURANCE,MATERIAL,SERVICE - 默认选择课时
    couponDiscountType: "FULL_REDUCTION", // 优惠类型: 默认满减券
    totalIssue: 0, // 总发行量
    distributionStartTime: 0, // 发放开始时间
    distributionEndTime: 0, // 发放结束时间
    startTime: 0, // 使用开始时间
    endTime: 0, // 使用结束时间
    remarks: null, // 备注
    enabled: true, // 启用状态
    coursePeriodIds: [], // 可使用课期ID数组
    discountAmount: 0, // 优惠值(满减/固定金额为金额,折扣为百分比)
    conditionAmount: 0, // 满减条件金额
    isUseLimit: false, // 使用是否有限制 - 默认不限制
    noLimitNumber: 0, // 每人限领数量
    couponScope: "ALL", // 优惠券使用范围: ALL(通用),LIMIT(指定)
    usedCourse: "ALL", // 前端使用范围标识 - 默认设置为'ALL'(通用)
    maxAmount: "", // 满金额(前端显示用)
    minAmount: "", // 减金额(前端显示用)

    // 新增字段用于处理使用时间逻辑
    useTimeType: 1, // 使用时间类型: 1-不限, 2-有限
    useTimeRange: [], // 使用时间范围(当useTimeType=2时使用)
    distributionTime: [] // 发放时间范围
  });

  // 添加 form 的监听器用于调试
  watch(
    () => form.usedCourse,
    (newVal, oldVal) => {
      console.log("🍪-----form.usedCourse changed-----", { oldVal, newVal });
    },
    { immediate: true }
  );

  // 监听使用时间类型变化
  watch(
    () => form.useTimeType,
    (newVal, oldVal) => {
      console.log("🍪-----form.useTimeType changed-----", { oldVal, newVal });
      if (newVal === 1) {
        // 选择"不限"时，清空时间范围
        form.useTimeRange = [];
        form.startTime = 0;
        form.endTime = 0;
        form.isUseLimit = false; // 同步更新isUseLimit字段
      } else if (newVal === 2) {
        // 选择"有限"时，设置为有限制
        form.isUseLimit = true; // 同步更新isUseLimit字段
      }
    },
    { immediate: true }
  );

  // 监听使用时间范围变化
  watch(
    () => form.useTimeRange,
    newVal => {
      console.log("🍪-----form.useTimeRange changed-----", newVal);
      if (
        form.useTimeType === 2 &&
        Array.isArray(newVal) &&
        newVal.length === 2
      ) {
        // 当选择"有限"且设置了时间范围时，更新API字段
        form.startTime = new Date(newVal[0]).getTime();
        form.endTime = new Date(newVal[1]).getTime();
      }
    },
    { deep: true }
  );

  // 监听优惠券类型变化
  watch(
    () => form.couponDiscountType,
    (newVal, oldVal) => {
      console.log("🍪-----form.couponDiscountType changed-----", {
        oldVal,
        newVal
      });
      // 由于只有满减券类型，这里主要用于调试
    }
  );

  // 监听费用类型变化
  watch(
    () => form.feeType,
    (newVal, oldVal) => {
      console.log("🍪-----form.feeType changed-----", { oldVal, newVal });
    }
  );

  const formRef = ref(null);
  const richFlag = ref(false);

  // 表单文件数据
  const formFile = ref({
    institutionLicense: [],
    qualificationDocuments: [],
    logo: [],
    video: [],
    environment: []
  });

  // 基本信息表单配置 - 根据API文档字段映射
  const formData = ref([
    {
      label: "优惠券名称",
      type: "input",
      prop: "name",
      check: true,
      width: "400px",
      placeholder: "请输入优惠券名称"
    },
    {
      label: "优惠券费用类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "feeType",
      placeholder: "请选择费用类型",
      options: [
        { name: "课时", value: "CLASS_HOUR" },
        // { name: "保险", value: "INSURANCE" },
        { name: "材料", value: "MATERIAL" }
        // { name: "服务", value: "SERVICE" }
      ]
    },
    {
      label: "优惠券类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "couponDiscountType",
      placeholder: "请选择优惠类型",
      options: [{ name: "满减券", value: "FULL_REDUCTION" }]
    },
    {
      label: "",
      type: "radioInput",
      check: false,
      prop: "fullReduction",
      maxLength: 30,
      width: "400px",
      placeholder: "满减条件设置",
      // 只有当选择满减券时才显示
      show: () => form.couponDiscountType === "FULL_REDUCTION"
    },
    {
      label: "发行数量",
      type: "input",
      typeInput: "number",
      check: true,
      maxLength: 11,
      width: "400px",
      prop: "totalIssue",
      placeholder: "请输入发行数量"
    },
    {
      label: "发放时间",
      type: "date",
      check: true,
      prop: "distributionTime",
      maxLength: 20,
      width: "400px",
      placeholder: "请选择发放时间"
    },
    {
      label: "使用时间限制",
      type: "radio",
      check: true,
      prop: "useTimeType",
      maxLength: 20,
      placeholder: "请选择使用时间限制",
      options: [
        { name: "不限", value: 1 },
        { name: "有限", value: 2 }
      ]
    },
    {
      label: "",
      type: "date",
      check: false,
      prop: "useTimeRange",
      width: "400px",
      placeholder: "请选择使用时间范围",
      // 只有当useTimeType为2时才显示
      show: () => form.useTimeType === 2
    },

    {
      label: "备注",
      type: "textarea",
      prop: "remarks",
      width: "400px",
      maxLength: 200,
      placeholder: "请输入备注"
    },
    {
      label: "状态",
      type: "radio",
      check: true,
      prop: "enabled",
      maxLength: 50,
      placeholder: "请选择状态",
      options: [
        { name: "启用", value: true },
        { name: "停用", value: false }
      ]
    }
  ]);

  // 使用范围表单配置
  const formData2 = ref([
    {
      label: "使用范围",
      type: "radio",
      check: true,
      maxLength: 50,
      width: "400px",
      prop: "usedCourse",
      placeholder: "请选择使用范围",
      options: [
        { name: "通用", value: "ALL" },
        { name: "指定", value: "LIMIT" }
      ]
    }
  ]);

  // 优惠券使用范围相关数据 - 根据API文档参数
  const scopeForm = reactive({
    courseName: "",
    coursePeriodName: "",
    startTime: "",
    endTime: "",
    coursePeriodState: ""
  });

  // 搜索列配置 - 根据API文档参数
  const searchColumns = ref([
    {
      label: "课程名称",
      prop: "courseName",
      type: "input",
      placeholder: "请输入课程名称",
      span: 6
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      type: "input",
      placeholder: "请输入课期名称",
      span: 6
    }
    // {
    //   label: "开始时间",
    //   prop: "startTime",
    //   type: "date",
    //   placeholder: "请选择开始时间",
    //   span: 6
    // },
    // {
    //   label: "结束时间",
    //   prop: "endTime",
    //   type: "date",
    //   placeholder: "请选择结束时间",
    //   span: 6
    // },
    // {
    //   label: "课程状态",
    //   prop: "coursePeriodState",
    //   type: "select",
    //   placeholder: "请选择课程状态",
    //   span: 6,
    //   options: [
    //     { label: "全部", value: "" },
    //     { label: "上线", value: "ONLINE" },
    //     { label: "下线", value: "OFFLINE" }
    //   ]
    // }
  ]);

  // 表格列配置 - 根据API返回数据结构
  const tableColumns = ref([
    {
      type: "selection",
      width: 55,
      align: "center"
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 150
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      minWidth: 200
    },
    {
      label: "期号",
      prop: "termNumber",
      width: 120,
      align: "center"
      //   formatter: ({ startTime }) => {
      //     if (startTime) {
      //       return new Date(startTime).toLocaleDateString();
      //     }
      //     return "-";
      //   }
    },
    {
      label: "开课时间",
      prop: "openTime",
      width: 120,
      align: "center",
      render: (cellValue, row) => {
        return formatTime(row.row.openTime, "YYYY-MM-DD HH:mm");
      }
    },
    {
      label: "购买类型",
      prop: "buyType",
      width: 120,
      align: "center",
      render: (cellValue, row) => {
        const buyType = row.row.buyType;
        // 购买类型映射
        const buyTypeMap = {
          ORDINARY: "普通单",
          PRIVATE_DOMAIN_GROUP_ORDER: "团购单"
        };
        return buyTypeMap[buyType] || buyType || "-";
      }
    },
    {
      label: "课程状态",
      prop: "coursePeriodState",
      width: 100,
      align: "center",
      render: (cellValue, row) => {
        const state = row.row.coursePeriodState;
        // 课期状态映射
        const stateMap = {
          OFFLINE: "下线",
          OFFLINE_UNDER_REVIEW: "下线审核中",
          ONLINE: "上线",
          ONLINE_UNDER_REVIEW: "上线审核中",
          COMPLETED: "已完成",
          NOT_LISTED: "未上架"
        };

        // 根据状态返回不同颜色的文本
        const stateText = stateMap[state] || state || "-";
        // let color = "#606266"; // 默认颜色

        // switch (state) {
        //   case "ONLINE":
        //     color = "#67c23a"; // 绿色 - 上线
        //     break;
        //   case "ONLINE_UNDER_REVIEW":
        //     color = "#e6a23c"; // 橙色 - 上线审核中
        //     break;
        //   case "OFFLINE":
        //     color = "#909399"; // 灰色 - 下线
        //     break;
        //   case "OFFLINE_UNDER_REVIEW":
        //     color = "#f56c6c"; // 红色 - 下线审核中
        //     break;
        //   case "COMPLETED":
        //     color = "#409eff"; // 蓝色 - 已完成
        //     break;
        //   case "NOT_LISTED":
        //     color = "#909399"; // 灰色 - 未上架
        //     break;
        // }

        return stateText;
      }
    }
  ]);

  // 初始数据 - 课程列表
  const tableData = ref([]);

  // 确保表格数据的稳定性
  const stableTableData = computed(() => {
    return tableData.value.map(item => {
      return {
        courseName: item.course.name,
        coursePeriodName: item.name,
        termNumber: item.termNumber,
        openTime: item.openTime,
        buyType: item.buyType,
        coursePeriodState: item.coursePeriodState
      };
    });
  });

  // 分页配置 - 根据API文档，page从0开始
  const pagination = reactive({
    page: 0,
    size: 10,
    total: 0
  });

  // 加载状态
  const loading = ref(false);
  const getListLoading = ref(false);

  // 选中的行
  const selectedRows = ref([]);

  // 计算属性：是否显示使用范围选择区域
  const showScopeSelection = computed(() => {
    console.log("🍪-----form.usedCourse-----", form.usedCourse);
    console.log("🍪-----showScopeSelection-----", form.usedCourse === "LIMIT");
    return form.usedCourse === "LIMIT"; // 当选择"指定"时显示
  });

  // 搜索方法 - 调用真实API
  const onSearch = async () => {
    if (loading.value) return; // 防止重复搜索

    // 通用模式下不需要搜索课程
    if (form.usedCourse === "ALL") {
      console.log("🍪-----通用模式：无需搜索课程");
      loading.value = false;
      return;
    }

    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: Number(pagination.size),
        sort: "createdAt,desc",
        courseName: scopeForm.courseName || undefined,
        coursePeriodName: scopeForm.coursePeriodName || undefined
        // startTime: scopeForm.startTime
        //   ? new Date(scopeForm.startTime).getTime()
        //   : undefined,
        // endTime: scopeForm.endTime
        //   ? new Date(scopeForm.endTime).getTime()
        //   : undefined,
        // coursePeriodState: scopeForm.coursePeriodState || undefined
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      console.log("查询参数:", params);
      //   return

      // 调用API
      const result = await getCouponCourse(params);
      console.log("API响应:", result);

      if (result.code === 200) {
        // tableData.value = result.data.content || [];
        pagination.total = result.data.totalElements || 0;
        pagination.page = result.data.number || 0;
        pagination.size = result.data.size || 20;
        tableData.value = result.data?.content?.map(item => {
          console.log(item);
          return {
            id: item.id,
            courseName: item.course?.name || "-",
            coursePeriodName: item?.name || "-",
            termNumber: item?.termNumber || "-",
            openTime: item?.openTime || "-",
            buyType: item?.buyType || "-",
            coursePeriodState: item?.coursePeriodState || "-"
          };
        });
        console.log(tableData.value);

        // console.log("🍪-----数据加载成功-----", {
        //   tableData: tableData.value,
        //   total: pagination.total,
        //   page: pagination.page,
        //   size: pagination.size
        // });
      } else {
        ElMessage.error(result.msg || "查询失败");
        console.error("🍪-----API调用失败-----", result);
      }
    } catch (error) {
      console.error("查询课程失败:", error);
      ElMessage.error("查询课程失败");
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化时不加载课程列表
  //   const initializePage = async () => {
  //     console.log("🍪-----页面初始化-----");
  //     // 通用模式下不需要加载课程列表，因为所有课程都可以使用
  //     console.log("🍪-----当前使用范围:", form.usedCourse);
  //     console.log("🍪-----通用模式：无需查询课程列表");
  //   };

  // 重置方法
  const onReset = () => {
    Object.keys(scopeForm).forEach(key => {
      scopeForm[key] = "";
    });
    pagination.page = 0;
    onSearch();
  };

  // 分页改变 - 根据API文档，page从0开始
  const onPageChange = page => {
    if (pagination.page === page) return; // 防止重复更新

    pagination.page = page - 1; // 转换为API的page格式（从0开始）
    onSearch();
  };

  // 每页条数改变
  const onSizeChange = size => {
    if (pagination.size === size) return; // 防止重复更新

    pagination.size = size;
    pagination.page = 0; // 重置到第一页（API格式）
    onSearch();
  };

  // 选择改变
  const onSelectionChange = selection => {
    // 使用 nextTick 避免递归更新
    nextTick(() => {
      // 防止递归更新，比较数组内容而不是引用
      const isSameSelection =
        JSON.stringify(selectedRows.value) === JSON.stringify(selection.id);
      if (!isSameSelection) {
        selectedRows.value = [...selection]; // 使用展开运算符创建新数组
        console.log("选中的行:", selection);
      }
    });
  };

  // 监听使用范围变化
  const onScopeChange = value => {
    console.log("🍪-----onScopeChange value-----", value);
    console.log("🍪-----form.usedCourse before-----", form.usedCourse);

    // 使用 nextTick 避免在同一个事件循环中更新
    nextTick(() => {
      if (value === "ALL") {
        // 选择"通用"时，清空选中的课程
        selectedRows.value = [];
        form.couponScope = "ALL";
        form.coursePeriodIds = [];
        // 通用模式下不调用接口，因为所有课程都可以使用
        console.log("🍪-----通用模式：所有课程均可使用，无需查询具体课程");
      } else if (value === "LIMIT") {
        // 选择"指定"时，设置使用范围并加载课程列表
        form.couponScope = "LIMIT";
        // 指定模式下需要加载课程列表供选择
        onSearch();
      }
      console.log("使用范围变化:", value);
      console.log("🍪-----form.usedCourse after-----", form.usedCourse);
      console.log("🍪-----form.couponScope-----", form.couponScope);
    });
  };

  // 返回上一页
  const reset = () => {
    router.go(-1);
  };

  // 校验规则 - 根据API文档必填字段
  const rules = ref({
    name: [{ required: true, message: "请输入优惠券名称", trigger: "blur" }],
    feeType: [
      { required: false, message: "请选择费用类型", trigger: "change" }
    ],
    couponDiscountType: [
      { required: true, message: "请选择优惠类型", trigger: "change" }
    ],
    totalIssue: [
      { required: true, message: "请输入总发行量", trigger: "blur" }
    ],
    distributionStartTime: [
      { required: true, message: "请选择发放开始时间", trigger: "change" }
    ],
    distributionEndTime: [
      { required: true, message: "请选择发放结束时间", trigger: "change" }
    ],
    isUseLimit: [
      { required: true, message: "请选择使用是否有限制", trigger: "change" }
    ],
    couponScope: [
      { required: true, message: "请选择使用范围", trigger: "change" }
    ],
    enabled: [{ required: true, message: "请选择启用状态", trigger: "change" }]
  });

  // 处理时间字段转换
  const handleTimeField = timeValue => {
    if (!timeValue) return 0;
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      // 如果是日期范围数组，取开始时间
      return new Date(timeValue[0]).getTime();
    }
    if (typeof timeValue === "string") {
      return new Date(timeValue).getTime();
    }
    return timeValue;
  };

  // 处理发放时间
  const handleDistributionTime = timeValue => {
    if (!timeValue) return { start: 0, end: 0 };
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      return {
        start: new Date(timeValue[0]).getTime(),
        end: new Date(timeValue[1]).getTime()
      };
    }
    return { start: 0, end: 0 };
  };

  // 提交处理
  const onSubmit = async () => {
    if (getListLoading.value) return;

    try {
      // 表单验证
      const valid = await formRef.value.validate();

      if (!valid) {
        ElMessage.error("请完善表单信息");
        return;
      }

      // 验证必填字段
      if (!form.name || form.name.trim() === "") {
        ElMessage.warning("请输入优惠券名称");
        return;
      }

      if (!form.couponDiscountType) {
        ElMessage.warning("请选择优惠类型");
        return;
      }

      if (!form.totalIssue || parseInt(form.totalIssue) <= 0) {
        ElMessage.warning("请输入有效的总发行量");
        return;
      }

      if (
        !form.distributionTime ||
        !Array.isArray(form.distributionTime) ||
        form.distributionTime.length !== 2
      ) {
        ElMessage.warning("请选择发放时间");
        return;
      }

      // 验证使用范围
      if (form.usedCourse === "LIMIT" && selectedRows.value.length === 0) {
        ElMessage.warning("请至少选择一个课程");
        return;
      }

      // 验证使用时间
      if (
        form.useTimeType === 2 &&
        (!form.useTimeRange || form.useTimeRange.length !== 2)
      ) {
        ElMessage.warning("请选择使用时间范围");
        return;
      }

      // 构建API请求数据 - 严格按照API文档字段类型
      let paramsData = {
        // 必填字段
        name: form.name || "", // string - 名称
        couponDiscountType: form.couponDiscountType || "", // string - 优惠类型
        totalIssue: parseInt(form.totalIssue) || 0, // integer(int32) - 总发行量
        distributionStartTime: 0, // integer(int64) - 发放开始时间，将在下面处理
        distributionEndTime: 0, // integer(int64) - 结束发放时间，将在下面处理
        isUseLimit: Boolean(form.isUseLimit), // boolean - 使用是否有限制
        couponScope: form.couponScope || "ALL", // string - 优惠券使用范围
        enabled: Boolean(form.enabled), // boolean - 启用状态

        // 可选字段
        feeType: form.feeType || null, // string - 费用类型，可选
        conditionAmount: form.conditionAmount
          ? parseFloat(form.conditionAmount)
          : 0, // number - 满减条件金额
        discountAmount: form.discountAmount
          ? parseFloat(form.discountAmount)
          : 0, // number - 优惠值
        startTime: form.startTime || 0, // integer(int64) - 使用开始时间
        endTime: form.endTime || 0, // integer(int64) - 使用结束时间
        remarks: form?.remarks, // string - 备注
        noLimitNumber: form.noLimitNumber ? parseInt(form.noLimitNumber) : 0, // integer(int32) - 每人限领数量
        coursePeriodIds: Array.isArray(form.coursePeriodIds)
          ? form.coursePeriodIds
          : [] // array - 可使用课期
      };

      // 处理发放时间
      const distributionTime = handleDistributionTime(form.distributionTime);
      paramsData.distributionStartTime = distributionTime.start;
      paramsData.distributionEndTime = distributionTime.end;

      // 确保时间字段为整数类型
      paramsData.distributionStartTime =
        parseInt(paramsData.distributionStartTime) || 0;
      paramsData.distributionEndTime =
        parseInt(paramsData.distributionEndTime) || 0;
      paramsData.startTime = parseInt(paramsData.startTime) || 0;
      paramsData.endTime = parseInt(paramsData.endTime) || 0;

      // 如果选择"指定"使用范围，设置课期ID数组
      if (form.usedCourse === "LIMIT") {
        paramsData.coursePeriodIds = selectedRows.value.map(row => row.id);
      }

      // 处理满减券金额字段
      if (form.couponDiscountType === "FULL_REDUCTION") {
        // 满减券：满金额 -> conditionAmount，减金额 -> discountAmount
        if (form.maxAmount && parseFloat(form.maxAmount) > 0) {
          paramsData.conditionAmount = parseFloat(form.maxAmount);
        }
        if (form.minAmount && parseFloat(form.minAmount) > 0) {
          paramsData.discountAmount = parseFloat(form.minAmount);
        }
      }

      console.log("API请求数据:", paramsData);

      // 调用新增优惠券API
      getListLoading.value = true;
      const result = await addCoupon(paramsData);
      console.log("API响应:", result);

      if (result.code === 200) {
        ElMessage.success("优惠券创建成功！");
        router.go(-1);
      } else {
        ElMessage.error(result.message || "创建失败，请重试");
      }
    } catch (error) {
      console.error("API调用失败:", error);
      ElMessage.error("创建失败，请重试");
    } finally {
      getListLoading.value = false;
    }
  };

  // 提交表单
  const submitForm = debounce(
    () => {
      formRef.value.validate(valid => {
        if (valid) {
          console.log("🍪-----valid-----", valid);
          onSubmit();
        } else {
          console.log("表单校验失败");
        }
      });
    },
    1000,
    { immediate: true }
  );

  // 其他数据
  const newData = ref();
  const oldData = ref();

  // 测试方法
  //   const testScopeChange = () => {
  //     console.log("🍪-----测试点击指定-----");
  //     form.usedCourse = "LIMIT";
  //     console.log("🍪-----form.usedCourse 设置为 LIMIT-----", form.usedCourse);
  //     console.log("🍪-----showScopeSelection 值-----", showScopeSelection.value);
  //   };

  // 测试表单默认值
  const testFormDefaults = () => {
    console.log("🍪-----测试表单默认值-----");
    console.log("🍪-----费用类型默认值-----", {
      feeType: form.feeType,
      feeTypeLabel: form.feeType === "CLASS_HOUR" ? "课时" : "其他"
    });
    console.log("🍪-----优惠券类型默认值-----", {
      couponDiscountType: form.couponDiscountType,
      couponDiscountTypeLabel:
        form.couponDiscountType === "FULL_REDUCTION" ? "满减券" : "其他"
    });
    console.log("🍪-----使用范围默认值-----", {
      usedCourse: form.usedCourse,
      usedCourseLabel: form.usedCourse === "ALL" ? "通用" : "其他"
    });
  };

  // 测试使用范围切换
  const testScopeSwitch = () => {
    console.log("🍪-----测试使用范围切换-----");
    console.log("🍪-----当前使用范围:", form.usedCourse);

    // 测试切换到通用模式
    form.usedCourse = "ALL";
    console.log("🍪-----切换到通用模式后:", form.usedCourse);

    // 测试切换到指定模式
    form.usedCourse = "LIMIT";
    console.log("🍪-----切换到指定模式后:", form.usedCourse);

    // 切换回通用模式
    form.usedCourse = "ALL";
    console.log("🍪-----切换回通用模式:", form.usedCourse);
  };

  //   // 测试满减券类型处理
  //   const testCouponType = () => {
  //     console.log("🍪-----当前优惠券类型-----", form.couponDiscountType);
  //     console.log("🍪-----满减金额字段-----", {
  //       maxAmount: form.maxAmount,
  //       minAmount: form.minAmount
  //     });

  //     // 测试满减券
  //     form.couponDiscountType = "FULL_REDUCTION";
  //     form.maxAmount = "100";
  //     form.minAmount = "20";
  //     console.log("🍪-----设置满减券后-----", {
  //       couponDiscountType: form.couponDiscountType,
  //       maxAmount: form.maxAmount,
  //       minAmount: form.minAmount
  //     });

  //     // 验证API数据映射
  //     const testApiData = {
  //       couponDiscountType: form.couponDiscountType,
  //       conditionAmount: parseFloat(form.maxAmount),
  //       discountAmount: parseFloat(form.minAmount)
  //     };
  //     console.log("🍪-----API数据映射-----", testApiData);
  //   };

  // 加载课期信息
  const loadCoursePeriods = async coursePeriodIds => {
    try {
      // 这里需要调用获取课期详情的接口
      // 由于没有直接的接口，我们可以通过搜索接口来获取
      const params = {
        page: 0,
        size: 1000, // 获取足够多的数据
        sort: "createdAt,desc"
      };

      const result = await getCouponCourse(params);
      if (result.code === 200 && result.data?.content) {
        // 过滤出选中的课期
        const selectedContent = result.data.content.filter(item =>
          coursePeriodIds.includes(item.id)
        );

        // 设置表格数据
        tableData.value = selectedContent.map(item => ({
          id: item.id,
          courseName: item.course?.name || "-",
          coursePeriodName: item.name || "-",
          termNumber: item.termNumber || "-",
          openTime: item.openTime || "-",
          buyType: item.buyType || "-",
          coursePeriodState: item.coursePeriodState || "-"
        }));

        // 设置选中的行
        selectedRows.value = coursePeriodIds;

        console.log("🍪-----课期数据加载完成-----", tableData.value);
      }
    } catch (error) {
      console.error("🍪-----加载课期数据失败-----", error);
    }
  };

  // 加载优惠券数据
  const loadCouponData = async id => {
    try {
      loading.value = true;
      const result = await getCouponFindById({ id });
      console.log("��-----获取优惠券数据成功-----", result);

      if (result.code === 200 && result.data) {
        const couponData = result.data;

        // 回显表单数据
        form.name = couponData.name || "";
        form.feeType = couponData.feeType || "CLASS_HOUR";
        form.couponDiscountType =
          couponData.couponDiscountType || "FULL_REDUCTION";
        form.totalIssue = couponData.totalIssue || 0;
        form.remarks = couponData.remarks || "";
        form.enabled =
          couponData.enabled !== undefined ? couponData.enabled : true;
        form.couponScope = couponData.couponScope || "ALL";
        form.usedCourse = couponData.couponScope || "ALL"; // 同步使用范围

        // 处理满减金额
        if (couponData.couponDiscountType === "FULL_REDUCTION") {
          form.conditionAmount = couponData.conditionAmount || 0;
          form.discountAmount = couponData.discountAmount || 0;
          form.maxAmount = couponData.conditionAmount?.toString() || "";
          form.minAmount = couponData.discountAmount?.toString() || "";
        }

        // 处理发放时间
        if (
          couponData.distributionStartTime &&
          couponData.distributionEndTime
        ) {
          form.distributionTime = [
            new Date(couponData.distributionStartTime),
            new Date(couponData.distributionEndTime)
          ];
        }

        // 处理使用时间
        if (couponData.startTime && couponData.endTime) {
          form.useTimeType = 2; // 设置为有限
          form.useTimeRange = [
            new Date(couponData.startTime),
            new Date(couponData.endTime)
          ];
          form.isUseLimit = true;
        } else {
          form.useTimeType = 1; // 设置为不限
          form.isUseLimit = false;
        }

        // 处理使用范围
        if (
          couponData.couponScope === "LIMIT" &&
          couponData.coursePeriodIds &&
          couponData.coursePeriodIds.length > 0
        ) {
          form.usedCourse = "LIMIT";
          form.couponScope = "LIMIT";
          form.coursePeriodIds = couponData.coursePeriodIds;

          // 如果是指定范围，需要加载课程列表
          await loadCoursePeriods(couponData.coursePeriodIds);
        }

        console.log("🍪-----表单数据回显完成-----", form);
      } else {
        ElMessage.error("获取优惠券数据失败");
      }
    } catch (error) {
      console.error("��-----加载优惠券数据失败-----", error);
      ElMessage.error("加载优惠券数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化时加载数据
  const initializePage = async () => {
    console.log("��-----页面初始化-----");
    console.log(route.query);

    // 检查是否是编辑模式（复制）
    if (route.query.type === "edit") {
      console.log("🍪-----编辑模式，加载优惠券数据-----", route.query.id);
      const data = await loadCouponData(route.query.id);
      console.log("🎁-----data-----", data);
    } else {
      // 通用模式下不需要加载课程列表，因为所有课程都可以使用
      console.log("🍪-----新增模式-----");
      console.log("��-----当前使用范围:", form.usedCourse);
      console.log("🍪-----通用模式：无需查询课程列表");
    }
  };

  // 页面加载完成后初始化
  onMounted(() => {
    initializePage();
  });

  return {
    // 路由相关
    router,
    route,

    // 表单相关
    form,
    formRef,
    formData,
    formData2,
    rules,

    // 使用范围相关
    scopeForm,
    searchColumns,
    tableColumns,
    tableData,
    stableTableData,
    showScopeSelection,

    // 分页相关
    pagination,

    // 状态相关
    loading,
    getListLoading,
    selectedRows,
    richFlag,
    formFile,

    // 方法
    onSearch,
    onReset,
    onPageChange,
    onSizeChange,
    onSelectionChange,
    onScopeChange,
    reset,
    submitForm,
    onSubmit,

    // 测试方法
    // testScopeChange,
    // testCouponType,
    // testCourseQuery,
    testFormDefaults,
    testScopeSwitch,

    // 其他数据
    newData,
    oldData
  };
}
